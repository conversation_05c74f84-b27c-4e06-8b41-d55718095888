// Optimizer API service for scenario optimization

import { API_BASE_URL, getAuthHeaders } from '@/api/commonApi';

export interface OptimizationMetrics {
  carbonReduction: number; // percentage
  costSavings: number; // in currency
  energyEfficiency: number; // percentage
  implementationTime: number; // in months
  riskLevel: 'Low' | 'Medium' | 'High';
  feasibilityScore: number; // 0-100
}

export interface OptimizationResult {
  id: string;
  name: string;
  baselineScenario: string;
  optimizedScenario: string;
  metrics: OptimizationMetrics;
  status: 'running' | 'completed' | 'failed';
  progress: number; // 0-100
  createdAt: string;
  completedAt?: string;
  recommendations: string[];
  keyInsights: string[];
}

export interface OptimizationRequest {
  baseScenarioId: string;
  currentScenarioId: string;
  optimizationGoals: string[];
  constraints: any[];
}

// Local storage key for optimization results
const STORAGE_KEY = 'optimization_results';

// Initialize mock data if not exists
export const initializeMockData = () => {
  const existingData = localStorage.getItem(STORAGE_KEY);
  if (!existingData) {
    const mockResults: OptimizationResult[] = [
      {
        id: 'opt-1',
        name: 'Steel Production Optimization',
        baselineScenario: 'Current Steel Process',
        optimizedScenario: 'Efficient Steel Process',
        metrics: {
          carbonReduction: 25.5,
          costSavings: 180000,
          energyEfficiency: 18.2,
          implementationTime: 12,
          riskLevel: 'Medium',
          feasibilityScore: 85
        },
        status: 'completed',
        progress: 100,
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        recommendations: [
          'Implement heat recovery systems',
          'Upgrade to electric arc furnaces',
          'Optimize material flow processes'
        ],
        keyInsights: [
          'Major savings from energy efficiency improvements',
          'Carbon reduction primarily from process optimization'
        ]
      },
      {
        id: 'opt-2',
        name: 'Chemical Process Enhancement',
        baselineScenario: 'Standard Chemical Process',
        optimizedScenario: 'Green Chemical Process',
        metrics: {
          carbonReduction: 32.1,
          costSavings: 245000,
          energyEfficiency: 22.8,
          implementationTime: 18,
          riskLevel: 'High',
          feasibilityScore: 78
        },
        status: 'completed',
        progress: 100,
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        completedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        recommendations: [
          'Transition to renewable energy sources',
          'Implement catalytic process improvements',
          'Install advanced monitoring systems'
        ],
        keyInsights: [
          'High impact potential with significant investment required',
          'Technology risks need careful management'
        ]
      }
    ];
    localStorage.setItem(STORAGE_KEY, JSON.stringify(mockResults));
  }
};

// Call the real /optimizer API endpoint
export const callOptimizerAPI = async (): Promise<any> => {
  try {
    const response = await fetch(`${API_BASE_URL}/optimizer`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({}) // Send blank body as requested
    });

    if (!response.ok) {
      throw new Error(`Optimizer API failed with status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error calling optimizer API:', error);
    throw error;
  }
};

// Run optimization with progress tracking and real API integration
export const runOptimization = async (
  request: OptimizationRequest,
  onProgress?: (progress: number, step: string) => void
): Promise<OptimizationResult> => {
  const steps = [
    'Initializing optimization engine...',
    'Analyzing base scenario data...',
    'Processing current scenario modifications...',
    'Running optimization algorithms...',
    'Calculating environmental impact...',
    'Computing cost-benefit analysis...',
    'Generating recommendations...',
    'Finalizing results...'
  ];

  // Simulate optimization process with progress updates
  for (let i = 0; i < steps.length; i++) {
    const progress = ((i + 1) / steps.length) * 100;
    if (onProgress) {
      onProgress(progress, steps[i]);
    }

    // Call the real API when we reach the "Running optimization algorithms..." step
    if (i === 3) { // Index 3 is "Running optimization algorithms..."
      try {
        await callOptimizerAPI();
        console.log('Optimizer API called successfully');
      } catch (error) {
        console.error('Optimizer API call failed, continuing with mock data:', error);
        // Continue with mock data if API fails
      }
    }

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
  }

  // Generate mock result
  const result: OptimizationResult = {
    id: `opt-${Date.now()}`,
    name: `Optimization ${new Date().toLocaleDateString()}`,
    baselineScenario: request.baseScenarioId,
    optimizedScenario: request.currentScenarioId,
    metrics: {
      carbonReduction: 15 + Math.random() * 25, // 15-40%
      costSavings: 50000 + Math.random() * 200000, // $50k-$250k
      energyEfficiency: 10 + Math.random() * 20, // 10-30%
      implementationTime: 6 + Math.random() * 18, // 6-24 months
      riskLevel: ['Low', 'Medium', 'High'][Math.floor(Math.random() * 3)] as 'Low' | 'Medium' | 'High',
      feasibilityScore: 70 + Math.random() * 30 // 70-100
    },
    status: 'completed',
    progress: 100,
    createdAt: new Date().toISOString(),
    completedAt: new Date().toISOString(),
    recommendations: [
      'Implement energy-efficient technologies in high-consumption areas',
      'Consider renewable energy sources for 30% of total energy needs',
      'Optimize process scheduling to reduce peak energy demand',
      'Invest in waste heat recovery systems',
      'Upgrade to smart monitoring and control systems'
    ],
    keyInsights: [
      'Primary carbon reduction comes from energy efficiency improvements',
      'ROI expected within 18-24 months of implementation',
      'Risk factors are primarily related to technology adoption timeline',
      'Significant synergies identified between cost savings and environmental benefits'
    ]
  };

  // Save result to localStorage
  const existingResults = getOptimizationResults();
  const updatedResults = [...existingResults, result];
  localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedResults));

  return result;
};

// Get all optimization results
export const getOptimizationResults = (filter?: 'all' | 'recent' | 'high-impact'): OptimizationResult[] => {
  const data = localStorage.getItem(STORAGE_KEY);
  if (!data) {
    initializeMockData();
    return getOptimizationResults(filter);
  }

  let results: OptimizationResult[] = JSON.parse(data);

  // Apply filter
  if (filter === 'recent') {
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    results = results.filter(result => new Date(result.createdAt) > weekAgo);
  } else if (filter === 'high-impact') {
    results = results.filter(result => 
      result.metrics.carbonReduction > 20 || result.metrics.costSavings > 150000
    );
  }

  return results.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
};

// Get specific optimization result
export const getOptimizationResult = (id: string): OptimizationResult | null => {
  const results = getOptimizationResults();
  return results.find(result => result.id === id) || null;
};

// Delete optimization result
export const deleteOptimizationResult = (id: string): boolean => {
  try {
    const results = getOptimizationResults();
    const filteredResults = results.filter(result => result.id !== id);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredResults));
    return true;
  } catch (error) {
    console.error('Error deleting optimization result:', error);
    return false;
  }
};

// Clear all optimization results
export const clearOptimizationResults = (): void => {
  localStorage.removeItem(STORAGE_KEY);
};

// Export optimization result to JSON
export const exportOptimizationResult = (id: string): string | null => {
  const result = getOptimizationResult(id);
  if (!result) return null;
  
  return JSON.stringify(result, null, 2);
};

// Initialize mock data on module load
initializeMockData();
