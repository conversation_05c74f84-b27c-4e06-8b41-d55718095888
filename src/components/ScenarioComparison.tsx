import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Zap, 
  Leaf,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { runOptimization, OptimizationResult, OptimizationRequest } from '@/services/optimizerApi';



interface ScenarioComparisonProps {
  open: boolean;
  onClose: () => void;
  onComplete?: (result: OptimizationResult) => void;
  baseScenarioName?: string;
  currentScenarioName?: string;
}

export const ScenarioComparison: React.FC<ScenarioComparisonProps> = ({
  open,
  onClose,
  onComplete,
  baseScenarioName = 'Base Scenario',
  currentScenarioName = 'Current Scenario'
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [result, setResult] = useState<OptimizationResult | null>(null);
  const [activeTab, setActiveTab] = useState('summary');

  const steps = [
    'Initializing optimization engine...',
    'Analyzing base scenario data...',
    'Processing current scenario modifications...',
    'Running optimization algorithms...',
    'Calculating environmental impact...',
    'Computing cost-benefit analysis...',
    'Generating recommendations...',
    'Finalizing results...'
  ];

  const runOptimizationProcess = async () => {
    setIsRunning(true);
    setProgress(0);
    setResult(null);

    try {
      // Create optimization request
      const request: OptimizationRequest = {
        baseScenarioId: baseScenarioName,
        currentScenarioId: currentScenarioName,
        optimizationGoals: ['minimize_cost', 'reduce_emissions'],
        constraints: [] // Add constraints if available
      };

      // Call the API service with progress tracking
      const optimizationResult = await runOptimization(request, (progress, step) => {
        setProgress(progress);
        setCurrentStep(step);
      });

      setResult(optimizationResult);
      setIsRunning(false);
      setCurrentStep('Optimization completed successfully!');

      if (onComplete) {
        onComplete(optimizationResult);
      }
    } catch (error) {
      console.error('Optimization failed:', error);
      setIsRunning(false);
      setCurrentStep('Optimization failed. Please try again.');
      // Optionally show error state or retry option
    }
  };

  const handleClose = () => {
    if (!isRunning) {
      onClose();
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'Low': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'High': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Play size={20} />
            Scenario Optimization
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {!result ? (
            // Running/Setup Phase
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">
                  Comparing: {currentScenarioName} vs {baseScenarioName}
                </h3>
                <p className="text-muted-foreground">
                  {isRunning ? 'Running optimization analysis...' : 'Ready to start optimization'}
                </p>
              </div>

              {isRunning && (
                <div className="space-y-3">
                  <Progress value={progress} className="w-full" />
                  <div className="text-center text-sm text-muted-foreground">
                    {currentStep} ({Math.round(progress)}%)
                  </div>
                </div>
              )}

              <div className="flex justify-center gap-3">
                {!isRunning ? (
                  <Button onClick={runOptimizationProcess} className="bg-green-500 hover:bg-green-600">
                    <Play size={16} className="mr-2" />
                    Start Optimization
                  </Button>
                ) : (
                  <Button disabled className="bg-gray-400">
                    <Clock size={16} className="mr-2 animate-spin" />
                    Running...
                  </Button>
                )}
              </div>
            </div>
          ) : (
            // Results Phase
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CheckCircle className="text-green-500" size={20} />
                  <h3 className="text-lg font-semibold">Optimization Complete</h3>
                </div>
                <Badge className={getRiskColor(result.metrics.riskLevel)}>
                  {result.metrics.riskLevel} Risk
                </Badge>
              </div>

              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="summary">Summary</TabsTrigger>
                  <TabsTrigger value="details">Details</TabsTrigger>
                  <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
                </TabsList>

                <TabsContent value="summary" className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-1">
                          <Leaf size={16} className="text-green-500" />
                          Carbon Reduction
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-green-600">
                          {result.metrics.carbonReduction.toFixed(1)}%
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-1">
                          <DollarSign size={16} className="text-blue-500" />
                          Cost Savings
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-blue-600">
                          ${(result.metrics.costSavings / 1000).toFixed(0)}k
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-1">
                          <Zap size={16} className="text-yellow-500" />
                          Energy Efficiency
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-yellow-600">
                          +{result.metrics.energyEfficiency.toFixed(1)}%
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm flex items-center gap-1">
                          <Clock size={16} className="text-purple-500" />
                          Implementation
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-purple-600">
                          {result.metrics.implementationTime.toFixed(0)} mo
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Feasibility Score</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-3">
                        <Progress value={result.metrics.feasibilityScore} className="flex-1" />
                        <span className="font-semibold">{result.metrics.feasibilityScore.toFixed(0)}/100</span>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="details" className="space-y-4">
                  <div className="space-y-3">
                    <h4 className="font-semibold">Key Insights</h4>
                    {result.keyInsights.map((insight, index) => (
                      <div key={index} className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg">
                        <TrendingUp size={16} className="text-blue-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{insight}</span>
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="recommendations" className="space-y-4">
                  <div className="space-y-3">
                    <h4 className="font-semibold">Recommended Actions</h4>
                    {result.recommendations.map((recommendation, index) => (
                      <div key={index} className="flex items-start gap-2 p-3 bg-green-50 rounded-lg">
                        <CheckCircle size={16} className="text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{recommendation}</span>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button variant="outline" onClick={() => setResult(null)}>
                  <RotateCcw size={16} className="mr-2" />
                  Run Again
                </Button>
                <Button onClick={handleClose}>
                  Close
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
