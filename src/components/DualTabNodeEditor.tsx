import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Info, Lock, Edit3 } from 'lucide-react';

// Import the connection form component
import { ConnectionForm } from "@/components/ConnectionForm/ConnectionForm";
import { FormData, OutputForm } from "@/components/ConnectionForm/types";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info, Lock, Edit3 } from "lucide-react";
import { useConnectionFormData } from "@/hooks/useConnectionFormData";
import { useSectors } from "@/hooks/useSectors";

interface DualTabNodeEditorProps {
  open: boolean;
  onClose: () => void;
  onSave: (formData: any) => void;
  nodeId: string;
  baseScenarioData?: any; // Data from base scenario
  currentScenarioData?: any; // Current scenario data
  isNewNode?: boolean; // True if node doesn't exist in base scenario
  activityName?: string;
  technologyName?: string;
  baseScenarioName?: string; // Name of the base scenario
  currentScenarioName?: string; // Name of the current scenario
  availableNodes?: any[]; // Available nodes for source activity mapping
}

// Default form data factory function
const getDefaultFormData = (activityName: string = '', technologyName: string = ''): FormData => ({
  activity: activityName,
  technology: technologyName,
  startYear: "2000",
  endYear: "2075",
  customTechnology: "",
  customActivity: "",
  // CRITICAL FIX: Initialize arrays with one empty entry so InputsStep renders properly
  energyInputs: [{
    id: `energy-${Date.now()}`,
    source: "",
    unit: "GJ",
    cost: "",
    sec: "",
    sourceActivity: "Nil",
    technology: "Nil"
  }],
  emissions: [{
    id: `emission-${Date.now()}`,
    source: "",
    factor: "",
    unit: "kg"
  }],
  materialInputs: [{
    id: `material-${Date.now()}`,
    material: "",
    unit: "Tonnes",
    cost: "",
    smc: "",
    sourceActivity: "Nil",
    technology: "Nil"
  }],
  energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
  emission: { source: "", ef: "", unit: "kg" },
  matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
  byproductTechnology: "",
  byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
  byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
  financial: { capacity: "", capacityUnit: "Tonnes/day", capitalCostUnit: "", omCost: "" },
  financialEntries: {}
});

export const DualTabNodeEditor: React.FC<DualTabNodeEditorProps> = ({
  open,
  onClose,
  onSave,
  nodeId,
  baseScenarioData,
  currentScenarioData,
  isNewNode = false,
  activityName = '',
  technologyName = '',
  baseScenarioName = 'Base Scenario',
  currentScenarioName = 'Current Scenario',
  availableNodes = []
}) => {
  const [activeTab, setActiveTab] = useState<'base' | 'current'>('current');

  // Simplified state - ConnectionForm manages its own internal state
  const [activeTab, setActiveTab] = useState<'base' | 'current'>('current');
  const [currentFormData, setCurrentFormData] = useState<FormData>(getDefaultFormData(activityName, technologyName));
  const [outputs, setOutputs] = useState<OutputForm[]>([]);
  const [technologies, setTechnologies] = useState<string[]>([technologyName || "Technology 1"]);

  // Initialize form data from current or base scenario data
  useEffect(() => {
    if (open) {
      console.log('DualTabNodeEditor initializing...');

      let initialFormData = getDefaultFormData(activityName, technologyName);
      let initialOutputs: OutputForm[] = [];
      let initialTechnologies = [technologyName || "Technology 1"];

      // Load from current scenario data if available
      if (currentScenarioData?.formData) {
        initialFormData = { ...initialFormData, ...currentScenarioData.formData };
        initialOutputs = currentScenarioData.outputs || [];
        initialTechnologies = currentScenarioData.technologies || initialTechnologies;
      }
      // Otherwise load from base scenario data if available and not a new node
      else if (baseScenarioData && !isNewNode) {
        const baseFormData = baseScenarioData.formData || {};
        initialFormData = { ...initialFormData, ...baseFormData };
        initialOutputs = baseScenarioData.outputs || [];
        initialTechnologies = baseScenarioData.technologies || initialTechnologies;
      }

      setCurrentFormData(initialFormData);
      setOutputs(initialOutputs);
      setTechnologies(initialTechnologies);
    }
  }, [open, currentScenarioData, baseScenarioData, isNewNode, activityName, technologyName]);

  // Handle save - collect data from ConnectionForm and pass to parent
  const handleSave = () => {
    console.log('DualTabNodeEditor handleSave called');
    console.log('- currentFormData:', currentFormData);
    console.log('- outputs:', outputs);
    console.log('- technologies:', technologies);

    // Create complete form data structure matching ConnectionFormDialog
    const completeFormData = {
      formData: currentFormData,
      outputs: outputs,
      technologies: technologies,
      formCompleted: true,
    };

    console.log('Calling parent onSave with complete form data:', completeFormData);
    onSave(completeFormData);
    onClose();
  };


  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[90vh] flex flex-col overflow-hidden">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Edit3 size={20} />
            Edit Node: {activityName}
            {technologyName && (
              <Badge variant="outline" className="ml-2">
                {technologyName}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        {/* Higher-level tabs for Base vs Current Scenario */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'base' | 'current')} className="flex-1 flex flex-col overflow-hidden min-h-0">
          <TabsList className="grid w-full grid-cols-2 mb-4 flex-shrink-0">
            <TabsTrigger value="base" className="flex items-center gap-2">
              <Lock size={16} />
              <div className="flex flex-col items-start">
                <span className="font-medium">{baseScenarioName}</span>
                <span className="text-xs text-muted-foreground">Base Scenario</span>
              </div>
            </TabsTrigger>
            <TabsTrigger value="current" className="flex items-center gap-2">
              <Edit3 size={16} />
              <div className="flex flex-col items-start">
                <span className="font-medium">{currentScenarioName}</span>
                <span className="text-xs text-muted-foreground">Current Scenario</span>
              </div>
            </TabsTrigger>
          </TabsList>

          {/* Base Scenario Tab - Read-only form */}
          <TabsContent value="base" className="flex-1 overflow-hidden min-h-0">
            {isNewNode ? (
              <Alert className="m-4">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  This is a new node that doesn't exist in the base scenario.
                  No base scenario data is available for comparison.
                </AlertDescription>
              </Alert>
            ) : baseScenarioData ? (
              <div className="h-full flex flex-col overflow-hidden">
                <Alert className="mx-4 mb-4 flex-shrink-0">
                  <Lock className="h-4 w-4" />
                  <AlertDescription>
                    This is the read-only data from the base scenario. Use the "Current Scenario" tab to make changes.
                  </AlertDescription>
                </Alert>
                <div className="flex-1 overflow-hidden">
                  <ConnectionForm
                    formData={(() => {
                      const generalFormData = baseScenarioData?.formData || {};
                      console.log('Base scenario using general form data:', generalFormData);
                      return generalFormData;
                    })()}
                    onFormDataChange={() => {}}
                    onSave={() => {}}
                    onCancel={() => {}}
                    activityName={activityName}
                    technologyName={technologyName}
                    readOnly={true}
                    showSaveButton={false}
                    initialOutputs={baseScenarioData?.outputs}
                    initialTechnologies={baseScenarioData?.technologies}
                    availableNodes={availableNodes}
                  />
                </div>
              </div>
            ) : (
              <Alert className="m-4">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  No base scenario data available for this node.
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>

          {/* Current Scenario Tab - Editable form */}
          <TabsContent value="current" className="flex-1 overflow-hidden min-h-0">
            <div className="h-full flex flex-col overflow-hidden">
              <Alert className="mx-4 mb-4 flex-shrink-0">
                <Edit3 className="h-4 w-4" />
                <AlertDescription>
                  Make your changes here. This data will be saved to the current scenario.
                </AlertDescription>
              </Alert>
              <div className="flex-1 overflow-hidden">
                <ConnectionForm
                  formData={currentFormData}
                  onFormDataChange={setCurrentFormData}
                  onSave={() => {}}
                  onCancel={() => {}}
                  activityName={activityName}
                  technologyName={technologyName}
                  readOnly={false}
                  showSaveButton={false}
                  initialOutputs={outputs}
                  initialTechnologies={technologies}
                  onOutputsChange={setOutputs}
                  onTechnologiesChange={setTechnologies}
                  availableNodes={availableNodes}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Dialog Footer with Save/Cancel buttons */}
        <DialogFooter className="flex-shrink-0 mt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
            Edit Node: {activityName}
            {technologyName && (
              <Badge variant="outline" className="ml-2">
                {technologyName}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        {/* Higher-level tabs for Base vs Current Scenario */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'base' | 'current')} className="flex-1 flex flex-col overflow-hidden min-h-0">
          <TabsList className="grid w-full grid-cols-2 mb-4 flex-shrink-0">
            <TabsTrigger value="base" className="flex items-center gap-2">
              <Lock size={16} />
              <div className="flex flex-col items-start">
                <span className="font-medium">{baseScenarioName}</span>
                <span className="text-xs text-muted-foreground">Base Scenario</span>
              </div>
            </TabsTrigger>
            <TabsTrigger value="current" className="flex items-center gap-2">
              <Edit3 size={16} />
              <div className="flex flex-col items-start">
                <span className="font-medium">{currentScenarioName}</span>
                <span className="text-xs text-muted-foreground">Current Scenario</span>
              </div>
            </TabsTrigger>
          </TabsList>

          {/* Base Scenario Tab - Read-only form */}
          <TabsContent value="base" className="flex-1 overflow-hidden min-h-0">
            {isNewNode ? (
              <Alert className="m-4">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  This is a new node that doesn't exist in the base scenario.
                  No base scenario data is available for comparison.
                </AlertDescription>
              </Alert>
            ) : baseScenarioData ? (
              <div className="h-full flex flex-col overflow-hidden">
                <Alert className="mx-4 mb-4 flex-shrink-0">
                  <Lock className="h-4 w-4" />
                  <AlertDescription>
                    This is the read-only data from the base scenario. Use the "Current Scenario" tab to make changes.
                  </AlertDescription>
                </Alert>

                {/* Read-only form display using ConnectionForm */}
                <div className="flex-1 overflow-y-auto px-4 pb-4">
                  <ConnectionForm
                    key={`base-scenario-${nodeId}`}
                    formData={(() => {
                      // Use technology-specific form data if available, otherwise fall back to general formData
                      if (baseScenarioData?.technologyFormData && technologyName && baseScenarioData.technologyFormData[technologyName]) {
                        const techFormData = baseScenarioData.technologyFormData[technologyName];
                        console.log('Base scenario using technology-specific form data for', technologyName, ':', techFormData);
                        console.log('Material inputs:', techFormData.materialInputs);
                        console.log('Financial data:', techFormData.financial);
                        return techFormData;
                      }
                      const generalFormData = baseScenarioData?.formData || {};
                      console.log('Base scenario using general form data:', generalFormData);
                      return generalFormData;
                    })()}
                    onFormDataChange={() => {}}
                    onSave={() => {}}
                    onCancel={() => {}}
                    activityName={activityName}
                    technologyName={technologyName}
                    readOnly={true}
                    showSaveButton={false}
                    initialOutputs={baseScenarioData?.outputs}
                    initialTechnologies={baseScenarioData?.technologies}
                    availableNodes={availableNodes}
                  />
                </div>
              </div>
            ) : (
              <Alert className="m-4">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  No base scenario data available for this node.
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>

          {/* Current Scenario Tab - Editable form using ConnectionForm */}
          <TabsContent value="current" className="flex-1 overflow-hidden min-h-0">
            <div className="h-full flex flex-col overflow-hidden">
              <Alert className="mx-4 mb-4 flex-shrink-0">
                <Edit3 className="h-4 w-4" />
                <AlertDescription>
                  Make your changes here. This data will be saved to the current scenario.
                  {!isNewNode && baseScenarioData && " The form is pre-populated with base scenario data."}
                </AlertDescription>
              </Alert>

              {/* Use the actual ConnectionForm component for consistency */}
              <div className="flex-1 overflow-hidden">
                <ConnectionForm
                  formData={currentFormData}
                  onFormDataChange={setCurrentFormData}
                  onSave={() => {}} // Handled by parent dialog
                  onCancel={() => {}} // Handled by parent dialog
                  activityName={activityName}
                  technologyName={activeTechnology}
                  readOnly={false}
                  showSaveButton={false}
                  initialOutputs={outputs}
                  initialTechnologies={technologies}
                  onOutputsChange={setOutputs}
                  onTechnologiesChange={setTechnologies}
                  availableNodes={availableNodes}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex-shrink-0">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-500 hover:bg-green-600 text-white">
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
