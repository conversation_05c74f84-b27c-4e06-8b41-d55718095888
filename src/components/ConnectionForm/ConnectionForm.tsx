import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { InputsStep } from './InputsStep';
import { OutputsStep } from './OutputsStep';
import { ByProductsStep } from './ByProductsStep';
import { FinancialStep } from './FinancialStep';
import { FormData, STEPS, OutputForm } from './types';

interface ConnectionFormProps {
  formData: FormData;
  onFormDataChange: (formData: FormData) => void;
  onSave: () => void;
  onCancel: () => void;
  activityName?: string;
  technologyName?: string;
  readOnly?: boolean;
  showSaveButton?: boolean;
  initialOutputs?: OutputForm[];
  initialTechnologies?: string[];
  // New callbacks to expose internal state
  onOutputsChange?: (outputs: OutputForm[]) => void;
  onTechnologiesChange?: (technologies: string[]) => void;
  availableNodes?: any[]; // Available nodes for source activity mapping
}

export const ConnectionForm: React.FC<ConnectionFormProps> = ({
  formData,
  onFormDataChange,
  onSave,
  onCancel,
  activityName = '',
  technologyName = '',
  readOnly = false,
  showSaveButton = true,
  initialOutputs,
  initialTechnologies,
  onOutputsChange,
  onTechnologiesChange,
  availableNodes = []
}) => {
  const [activeTab, setActiveTab] = useState('inputs');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // State for outputs (needed by OutputsStep and FinancialStep)
  const [outputs, setOutputs] = useState<OutputForm[]>([]);
  const [activeOutputTab, setActiveOutputTab] = useState('0');

  // State for technologies (needed by various steps)
  const [technologies, setTechnologies] = useState<string[]>([technologyName || 'Technology 1']);
  const [activeTechnology, setActiveTechnology] = useState(technologyName || 'Technology 1');

  // Initialize outputs and technologies from props or formData
  useEffect(() => {
    // Initialize outputs from props first, then fallback to formData
    if (initialOutputs && Array.isArray(initialOutputs)) {
      setOutputs(initialOutputs);
      if (onOutputsChange) {
        onOutputsChange(initialOutputs);
      }
    } else if (formData && typeof formData === 'object' && 'outputs' in formData && Array.isArray(formData.outputs)) {
      const outputsFromFormData = formData.outputs as OutputForm[];
      setOutputs(outputsFromFormData);
      if (onOutputsChange) {
        onOutputsChange(outputsFromFormData);
      }
    } else {
      setOutputs([]);
    }

    // Initialize technologies from props first, then fallback to defaults
    if (initialTechnologies && Array.isArray(initialTechnologies)) {
      setTechnologies(initialTechnologies);
      if (onTechnologiesChange) {
        onTechnologiesChange(initialTechnologies);
      }
      if (initialTechnologies.length > 0) {
        setActiveTechnology(initialTechnologies[0]);
      }
    } else if (technologyName) {
      // Use the technologyName prop if available
      const techArray = [technologyName];
      setTechnologies(techArray);
      if (onTechnologiesChange) {
        onTechnologiesChange(techArray);
      }
      setActiveTechnology(technologyName);
    }
  }, [formData, initialOutputs, initialTechnologies, readOnly, activityName, technologyName]);

  const updateField = (field: string, value: any) => {
    if (readOnly) return;
    
    const updatedFormData = {
      ...formData,
      [field]: value
    };
    onFormDataChange(updatedFormData);
  };

  const updateFormField = (section: string, field: string, value: string) => {
    if (readOnly) return;

    const updatedFormData = {
      ...formData,
      [section]: {
        ...formData[section as keyof FormData],
        [field]: value
      }
    };
    onFormDataChange(updatedFormData);
  };

  // Function needed by OutputsStep
  const updateOutputField = (fieldPath: string, value: any) => {
    if (readOnly) return;

    // Parse the field path (e.g., "outputs.0.energyOutputs.0.energy")
    const pathParts = fieldPath.split('.');

    if (pathParts[0] === 'outputs') {
      const outputIndex = parseInt(pathParts[1]);
      const updatedOutputs = [...outputs];

      if (pathParts.length === 3) {
        // Direct output field (e.g., "outputs.0.targetNode")
        updatedOutputs[outputIndex] = {
          ...updatedOutputs[outputIndex],
          [pathParts[2]]: value
        };
      } else if (pathParts.length === 5) {
        // Nested field (e.g., "outputs.0.energyOutputs.0.energy")
        const arrayField = pathParts[2] as 'energyOutputs' | 'materialOutputs';
        const itemIndex = parseInt(pathParts[3]);
        const field = pathParts[4];

        updatedOutputs[outputIndex] = {
          ...updatedOutputs[outputIndex],
          [arrayField]: updatedOutputs[outputIndex][arrayField].map((item: any, idx: number) =>
            idx === itemIndex ? { ...item, [field]: value } : item
          )
        };
      }

      setOutputs(updatedOutputs);

      // Notify parent of outputs change
      if (onOutputsChange) {
        onOutputsChange(updatedOutputs);
      }

      // Also update the form data
      const updatedFormData = {
        ...formData,
        outputs: updatedOutputs
      };
      onFormDataChange(updatedFormData);
    }
  };

  // Function to clear errors (needed by OutputsStep)
  const clearError = (errorKey: string) => {
    const newErrors = { ...errors };
    delete newErrors[errorKey];
    setErrors(newErrors);
  };

  // Wrapper function for setOutputs that also calls the callback
  const handleSetOutputs = (newOutputs: OutputForm[]) => {
    setOutputs(newOutputs);
    if (onOutputsChange) {
      onOutputsChange(newOutputs);
    }
  };

  const handleSave = () => {
    if (readOnly) return;
    onSave();
  };

  return (
    <div className={`space-y-6 ${readOnly ? 'read-only-form' : ''}`}>
      {readOnly && (
        <style>{`
          .read-only-form input,
          .read-only-form select,
          .read-only-form textarea {
            background-color: #f1f3f4 !important;
            color: #5f6368 !important;
            cursor: not-allowed !important;
            opacity: 0.8 !important;
            border-color: #dadce0 !important;
            pointer-events: none !important;
          }
          .read-only-form button:not([data-state]):not([role="tab"]) {
            display: none !important;
          }
          .read-only-form [data-radix-select-trigger] {
            background-color: #f1f3f4 !important;
            color: #5f6368 !important;
            cursor: not-allowed !important;
            opacity: 0.8 !important;
            border-color: #dadce0 !important;
            pointer-events: none !important;
          }
          .read-only-form [data-radix-select-trigger]:hover {
            background-color: #f1f3f4 !important;
          }
          .read-only-form [data-radix-select-trigger] svg {
            display: none !important;
          }
          .read-only-form [data-radix-select-trigger]:after {
            display: none !important;
          }
          .read-only-form [data-radix-select-icon] {
            display: none !important;
          }
          .read-only-form [data-radix-select-content] {
            display: none !important;
          }
          .read-only-form [data-radix-select-viewport] {
            display: none !important;
          }
          /* Disable all interactive elements */
          .read-only-form * {
            user-select: none !important;
          }
          .read-only-form input:focus,
          .read-only-form select:focus,
          .read-only-form textarea:focus {
            outline: none !important;
            box-shadow: none !important;
          }
        `}</style>
      )}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="inputs">Inputs</TabsTrigger>
          <TabsTrigger value="outputs">Outputs</TabsTrigger>
          <TabsTrigger value="byproducts">By-products</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
        </TabsList>

        <TabsContent value="inputs" className="space-y-4">
          <InputsStep
            formData={formData}
            updateField={updateField}
            updateFormField={updateFormField}
            errors={errors}
            usingManualEntry={false}
            energyInputAutoFillLabel=""
            matInputAutoFillLabel=""
            technologyAutoFillLabel={activeTechnology}
            availableTechnologies={technologies}
            availableNodes={availableNodes}
            technologies={technologies}
            activeTechnology={activeTechnology}
            setActiveTechnology={readOnly ? () => {} : setActiveTechnology}
            updateTechnologyName={() => {}}
            readOnly={readOnly}
          />
        </TabsContent>

        <TabsContent value="outputs" className="space-y-4">
          <OutputsStep
            outputs={outputs}
            setOutputs={handleSetOutputs}
            activeOutputTab={activeOutputTab}
            setActiveOutputTab={setActiveOutputTab}
            errors={errors}
            availableNodes={availableNodes}
            updateOutputField={updateOutputField}
            clearError={clearError}
            technologies={technologies}
            activeTechnology={activeTechnology}
            setActiveTechnology={setActiveTechnology}
            readOnly={readOnly}
          />
        </TabsContent>

        <TabsContent value="byproducts" className="space-y-4">
          <ByProductsStep
            formData={formData}
            updateFormField={updateFormField}
            updateField={updateField}
            errors={errors}
            availableNodes={[]}
            technologies={technologies}
            activeTechnology={activeTechnology}
            setActiveTechnology={setActiveTechnology}
            readOnly={readOnly}
          />
        </TabsContent>

        <TabsContent value="financial" className="space-y-4">
          <FinancialStep
            formData={formData}
            updateFormField={updateFormField}
            errors={errors}
            outputs={outputs}
            availableNodes={[]}
            technologies={technologies}
            readOnly={readOnly}
          />
        </TabsContent>
      </Tabs>

      {showSaveButton && !readOnly && (
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-green-500 hover:bg-green-600 text-white">
            Save Changes
          </Button>
        </div>
      )}

      {readOnly && (
        <div className="text-center text-sm text-muted-foreground pt-4 border-t">
          This data is read-only and cannot be modified.
        </div>
      )}
    </div>
  );
};
