// Simple test utility to verify the optimizer API integration
import { callOptimizerAPI, runOptimization, OptimizationRequest } from '@/services/optimizerApi';

// Test function to verify the API integration
export const testOptimizerAPI = async (): Promise<void> => {
  console.log('🧪 Testing Optimizer API Integration...');
  
  try {
    // Test 1: Direct API call
    console.log('📡 Testing direct API call...');
    const apiResult = await callOptimizerAPI();
    console.log('✅ Direct API call successful:', apiResult);
  } catch (error) {
    console.log('❌ Direct API call failed (expected for dummy API):', error);
  }

  try {
    // Test 2: Full optimization flow with progress tracking
    console.log('🔄 Testing full optimization flow...');
    
    const request: OptimizationRequest = {
      baseScenarioId: 'test-base-scenario',
      currentScenarioId: 'test-current-scenario',
      optimizationGoals: ['minimize_cost', 'reduce_emissions'],
      constraints: []
    };

    const result = await runOptimization(request, (progress, step) => {
      console.log(`📊 Progress: ${progress.toFixed(1)}% - ${step}`);
    });

    console.log('✅ Full optimization flow successful:', {
      id: result.id,
      name: result.name,
      status: result.status,
      progress: result.progress,
      carbonReduction: result.metrics.carbonReduction.toFixed(1) + '%',
      costSavings: '$' + (result.metrics.costSavings / 1000).toFixed(0) + 'k',
      riskLevel: result.metrics.riskLevel
    });

  } catch (error) {
    console.log('❌ Full optimization flow failed:', error);
  }

  console.log('🏁 Optimizer API integration test completed!');
};

// Export for use in browser console
(window as any).testOptimizerAPI = testOptimizerAPI;
